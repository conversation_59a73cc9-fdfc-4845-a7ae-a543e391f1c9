# DeepSearcher 问题解决方案

## 问题分析

你遇到了两个主要问题：

### 1. SentenceTransformer 参数错误
```
TypeError: SentenceTransformer.__init__() got an unexpected keyword argument 'uri'
```

**原因**: deepsearcher 的 MilvusEmbedding 实现中，错误地将 vector_db 的配置参数传递给了 SentenceTransformer。

### 2. DeepSeek API 余额不足
```
Error code: 402 - {'error': {'message': 'Insufficient Balance'
```

**原因**: DeepSeek API 账户余额不足。

## 解决方案

### ✅ 已修复的配置

我已经修改了你的 `main.py` 配置：

1. **Embedding 配置**: 保持使用 MilvusEmbedding，但确保只传递必要的参数
2. **LLM 配置**: 改为使用 OpenAI（需要 API key）

### 🔧 环境变量设置

创建 `.env` 文件并设置以下环境变量：

```bash
# 推荐使用 OpenAI（相对稳定）
export OPENAI_API_KEY=your_openai_api_key_here

# 或者如果你想继续使用 DeepSeek（需要充值）
export DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 解决 tokenizers 警告
export TOKENIZERS_PARALLELISM=false
```

### 🚀 运行步骤

1. **设置环境变量**:
   ```bash
   # 方法1: 直接设置
   export OPENAI_API_KEY=your_api_key_here
   
   # 方法2: 使用 .env 文件
   cp .env.example .env
   # 编辑 .env 文件，填入你的 API key
   ```

2. **测试配置**:
   ```bash
   python test_config.py
   ```

3. **运行主程序**:
   ```bash
   python main.py
   ```

## 🔍 其他选择

如果你没有 OpenAI API key，可以考虑以下选择：

### 1. 充值 DeepSeek 账户
- 访问 [DeepSeek 官网](https://platform.deepseek.com/)
- 充值账户余额
- 修改 `main.py` 中的 LLM 配置为 DeepSeek

### 2. 使用其他免费/便宜的 LLM 服务
- **SiliconFlow**: 提供免费额度
- **OpenRouter**: 多种模型选择
- **Ollama**: 本地运行（需要较好的硬件）

### 3. 使用本地模型
```python
# 使用 Ollama 本地模型
config.set_provider_config("llm", "Ollama", {"model": "qwen3"})
```

## 📝 配置示例

### OpenAI 配置
```python
config.set_provider_config("llm", "OpenAI", {"model": "gpt-3.5-turbo"})
```

### DeepSeek 配置（充值后）
```python
config.set_provider_config("llm", "DeepSeek", {"model": "deepseek-reasoner"})
```

### SiliconFlow 配置
```python
config.set_provider_config("llm", "SiliconFlow", {"model": "deepseek-ai/DeepSeek-R1"})
```

## ⚠️ 注意事项

1. **API Key 安全**: 不要将 API key 直接写在代码中，使用环境变量
2. **模型选择**: 推荐使用大型推理模型（如 deepseek-r1、o1-mini）以获得更好的效果
3. **网络问题**: 如果遇到 Hugging Face 连接问题，可以设置镜像：
   ```bash
   export HF_ENDPOINT=https://hf-mirror.com
   ```

## 🎯 下一步

配置完成后，你就可以：
1. 加载 PDF 文档
2. 进行智能问答
3. 生成详细报告

如果还有问题，请检查：
- API key 是否正确设置
- 网络连接是否正常
- 依赖包是否完整安装
