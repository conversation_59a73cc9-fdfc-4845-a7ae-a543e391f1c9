"""
替代的 LLM 配置选项
"""

# 选择 1: 使用 SiliconFlow (提供免费额度)
# 需要注册 https://siliconflow.cn/ 获取免费 API key
config.set_provider_config("llm", "SiliconFlow", {
    "model": "deepseek-ai/DeepSeek-R1"
})
# 环境变量: export SILICONFLOW_API_KEY=your_key

# 选择 2: 使用 OpenRouter (多种免费模型)
config.set_provider_config("llm", "OpenAI", {
    "model": "qwen/qwen3-235b-a22b:free",
    "base_url": "https://openrouter.ai/api/v1",
    "api_key": "your_openrouter_key"
})

# 选择 3: 使用本地 Ollama (完全免费，需要好的硬件)
# 先安装: brew install ollama
# 下载模型: ollama pull qwen3
config.set_provider_config("llm", "Ollama", {
    "model": "qwen3"
})

# 选择 4: 使用 TogetherAI (有免费额度)
config.set_provider_config("llm", "TogetherAI", {
    "model": "meta-llama/Llama-4-Scout-17B-16E-Instruct"
})
# 环境变量: export TOGETHER_API_KEY=your_key
