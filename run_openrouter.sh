#!/bin/bash

# 使用 OpenRouter API 运行 DeepSearcher
echo "🚀 启动 DeepSearcher with OpenRouter..."

# 激活虚拟环境
source .venv/bin/activate

# 设置环境变量
export OPENAI_API_KEY="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"
export OPENAI_BASE_URL="https://openrouter.ai/api/v1"
export TOKENIZERS_PARALLELISM=false

# 运行程序
echo "✅ 使用 OpenRouter API"
echo "🔍 模型: qwen/qwen-2.5-72b-instruct"
echo "📄 处理文件: tmpki79mvp__origin.pdf"
echo ""

python main_openrouter.py
