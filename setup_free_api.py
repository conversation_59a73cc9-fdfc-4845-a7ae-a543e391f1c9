#!/usr/bin/env python3
"""
快速设置免费 API 的脚本
"""

import os
import subprocess
import sys

def check_ollama():
    """检查 Ollama 是否安装"""
    try:
        result = subprocess.run(["ollama", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ollama 已安装")
            return True
    except FileNotFoundError:
        pass
    print("❌ Ollama 未安装")
    return False

def install_ollama():
    """安装 Ollama"""
    print("🔧 安装 Ollama...")
    if sys.platform == "darwin":  # macOS
        try:
            subprocess.run(["brew", "install", "ollama"], check=True)
            print("✅ Ollama 安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ Ollama 安装失败，请手动安装:")
            print("   brew install ollama")
            return False
    else:
        print("请手动安装 Ollama:")
        print("Linux: curl -fsSL https://ollama.ai/install.sh | sh")
        print("Windows: 下载 https://ollama.ai/download")
        return False

def setup_ollama_model():
    """设置 Ollama 模型"""
    print("📥 下载 Qwen3 模型...")
    try:
        subprocess.run(["ollama", "pull", "qwen3"], check=True)
        print("✅ Qwen3 模型下载成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ 模型下载失败")
        return False

def create_env_file():
    """创建 .env 文件"""
    env_content = """# 免费 LLM API Keys
# 选择其中一个设置即可

# 1. SiliconFlow (推荐，免费额度充足)
# 注册: https://siliconflow.cn/
# SILICONFLOW_API_KEY=your_siliconflow_key_here

# 2. OpenRouter (有免费模型)
# 注册: https://openrouter.ai/
# OPENROUTER_API_KEY=your_openrouter_key_here

# 3. OpenAI (付费但稳定)
# OPENAI_API_KEY=your_openai_key_here

# 4. DeepSeek (需要充值)
# DEEPSEEK_API_KEY=your_deepseek_key_here

# 解决 tokenizers 警告
TOKENIZERS_PARALLELISM=false
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)
    print("✅ 创建了 .env 文件模板")

def main():
    print("🚀 设置免费 LLM API...\n")
    
    # 检查当前环境变量
    print("📋 检查当前 API Keys:")
    apis = ["OPENAI_API_KEY", "DEEPSEEK_API_KEY", "SILICONFLOW_API_KEY", "OPENROUTER_API_KEY"]
    has_api = False
    
    for api in apis:
        if os.getenv(api):
            print(f"✅ {api}: 已设置")
            has_api = True
        else:
            print(f"❌ {api}: 未设置")
    
    if has_api:
        print("\n✅ 已有 API key，可以直接运行程序")
        print("如果遇到余额不足问题，请考虑以下选项:")
    else:
        print("\n❌ 没有找到任何 API key")
    
    print("\n🆓 免费选项推荐:")
    
    # 选项 1: SiliconFlow
    print("\n1️⃣ SiliconFlow (推荐)")
    print("   - 免费额度充足")
    print("   - 支持 DeepSeek-R1 模型")
    print("   - 注册: https://siliconflow.cn/")
    print("   - 设置: export SILICONFLOW_API_KEY=your_key")
    
    # 选项 2: OpenRouter
    print("\n2️⃣ OpenRouter")
    print("   - 有免费模型可用")
    print("   - 多种模型选择")
    print("   - 注册: https://openrouter.ai/")
    print("   - 设置: export OPENROUTER_API_KEY=your_key")
    
    # 选项 3: 本地 Ollama
    print("\n3️⃣ 本地 Ollama (完全免费)")
    if check_ollama():
        print("   - Ollama 已安装")
        print("   - 运行: ollama pull qwen3")
    else:
        print("   - 需要安装: brew install ollama")
        print("   - 然后运行: ollama pull qwen3")
        
        choice = input("\n是否现在安装 Ollama? (y/n): ").lower()
        if choice == 'y':
            if install_ollama():
                setup_ollama_model()
    
    # 创建 .env 文件
    if not os.path.exists(".env"):
        create_env_file()
        print("\n📝 请编辑 .env 文件，填入你的 API key")
    
    print("\n🎯 下一步:")
    print("1. 选择一个免费 API 并获取 key")
    print("2. 设置环境变量或编辑 .env 文件")
    print("3. 运行: python free_llm_config.py")
    
    print("\n💡 如果你想继续使用 DeepSeek:")
    print("   - 访问 https://platform.deepseek.com/")
    print("   - 充值账户余额")
    print("   - 然后运行原来的 main.py")

if __name__ == "__main__":
    main()
