#!/usr/bin/env python3
"""
测试 deepsearcher 配置的脚本
"""

import os
from deepsearcher.configuration import Configuration, init_config

def test_embedding_config():
    """测试 embedding 配置"""
    print("🔧 测试 embedding 配置...")
    
    config = Configuration()
    
    # 测试 MilvusEmbedding 配置
    try:
        config.set_provider_config(
            "embedding",
            "MilvusEmbedding",
            {
                "model": "BAAI/bge-base-en-v1.5",
            },
        )
        print("✅ MilvusEmbedding 配置成功")
    except Exception as e:
        print(f"❌ MilvusEmbedding 配置失败: {e}")
        return False
    
    # 测试向量数据库配置
    try:
        config.set_provider_config(
            "vector_db",
            "Milvus",
            {
                "default_collection": "deepsearcher",
                "uri": "./milvus.db",
                "token": "root:Mil<PERSON><PERSON>",
                "db": "default"
            },
        )
        print("✅ Milvus 向量数据库配置成功")
    except Exception as e:
        print(f"❌ Milvus 向量数据库配置失败: {e}")
        return False
    
    # 测试文件加载器配置
    try:
        config.set_provider_config(
            "file_loader",
            "PDFLoader",
            {},
        )
        print("✅ PDFLoader 配置成功")
    except Exception as e:
        print(f"❌ PDFLoader 配置失败: {e}")
        return False
    
    return True

def test_llm_config():
    """测试 LLM 配置"""
    print("\n🤖 测试 LLM 配置...")
    
    config = Configuration()
    
    # 检查是否有 OpenAI API key
    if os.getenv("OPENAI_API_KEY"):
        try:
            config.set_provider_config("llm", "OpenAI", {"model": "gpt-3.5-turbo"})
            print("✅ OpenAI LLM 配置成功")
            return True
        except Exception as e:
            print(f"❌ OpenAI LLM 配置失败: {e}")
    else:
        print("⚠️  未找到 OPENAI_API_KEY 环境变量")
    
    # 检查是否有 DeepSeek API key
    if os.getenv("DEEPSEEK_API_KEY"):
        try:
            config.set_provider_config("llm", "DeepSeek", {"model": "deepseek-reasoner"})
            print("✅ DeepSeek LLM 配置成功")
            return True
        except Exception as e:
            print(f"❌ DeepSeek LLM 配置失败: {e}")
    else:
        print("⚠️  未找到 DEEPSEEK_API_KEY 环境变量")
    
    print("❌ 没有可用的 LLM API key")
    return False

def main():
    """主函数"""
    print("🚀 开始测试 deepsearcher 配置...\n")
    
    embedding_ok = test_embedding_config()
    llm_ok = test_llm_config()
    
    print("\n📊 测试结果:")
    print(f"Embedding 配置: {'✅ 成功' if embedding_ok else '❌ 失败'}")
    print(f"LLM 配置: {'✅ 成功' if llm_ok else '❌ 失败'}")
    
    if embedding_ok and llm_ok:
        print("\n🎉 所有配置测试通过！可以运行 main.py")
    else:
        print("\n⚠️  部分配置有问题，请检查:")
        if not embedding_ok:
            print("  - 检查 pymilvus-model 是否正确安装")
        if not llm_ok:
            print("  - 设置 OPENAI_API_KEY 或 DEEPSEEK_API_KEY 环境变量")

if __name__ == "__main__":
    main()
