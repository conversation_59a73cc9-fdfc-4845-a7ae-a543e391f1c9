#!/usr/bin/env python3
"""
简单测试 OpenRouter API
"""

import openai
import os

def main():
    # 设置 API
    openrouter_key = "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"
    
    print("🧪 测试 OpenRouter API...")
    
    try:
        client = openai.OpenAI(
            api_key=openrouter_key,
            base_url="https://openrouter.ai/api/v1"
        )
        
        print("📡 发送测试请求...")
        response = client.chat.completions.create(
            model="qwen/qwen-2.5-72b-instruct",
            messages=[{"role": "user", "content": "Hello! Please say 'API working'"}],
            max_tokens=10
        )
        
        result = response.choices[0].message.content
        print(f"✅ 成功! 响应: {result}")
        
        # 如果基础 API 工作，尝试 deepsearcher
        print("\n🔧 测试 deepsearcher 配置...")
        test_deepsearcher(openrouter_key)
        
    except Exception as e:
        print(f"❌ API 测试失败: {e}")
        print("💡 尝试其他免费模型...")
        
        # 尝试其他模型
        try_other_models(openrouter_key)

def test_deepsearcher(api_key):
    """测试 deepsearcher 配置"""
    try:
        from deepsearcher.configuration import Configuration, init_config
        
        # 设置环境变量
        os.environ["OPENAI_API_KEY"] = api_key
        os.environ["OPENAI_BASE_URL"] = "https://openrouter.ai/api/v1"
        
        config = Configuration()
        
        # 只配置 LLM，不配置其他组件
        config.set_provider_config("llm", "OpenAI", {
            "model": "qwen/qwen-2.5-72b-instruct",
            "base_url": "https://openrouter.ai/api/v1",
            "api_key": api_key
        })
        
        print("✅ deepsearcher LLM 配置成功")
        
    except Exception as e:
        print(f"❌ deepsearcher 配置失败: {e}")

def try_other_models(api_key):
    """尝试其他免费模型"""
    models = [
        "microsoft/phi-3-mini-128k-instruct:free",
        "huggingface/CodeBERTa-small-v1",
        "google/gemma-2-9b-it:free"
    ]
    
    client = openai.OpenAI(
        api_key=api_key,
        base_url="https://openrouter.ai/api/v1"
    )
    
    for model in models:
        try:
            print(f"🔄 尝试模型: {model}")
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": "Hi"}],
                max_tokens=5
            )
            print(f"✅ {model} 工作正常!")
            return model
        except Exception as e:
            print(f"❌ {model} 失败: {e}")
    
    return None

if __name__ == "__main__":
    main()
