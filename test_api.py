#!/usr/bin/env python3
"""
测试 API 连接的脚本
"""

import os
import sys

def test_openai():
    """测试 OpenAI API"""
    try:
        import openai
        client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=10
        )
        print("✅ OpenAI API 连接成功")
        return True
    except Exception as e:
        print(f"❌ OpenAI API 连接失败: {e}")
        return False

def test_deepseek():
    """测试 DeepSeek API"""
    try:
        import openai
        client = openai.OpenAI(
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            base_url="https://api.deepseek.com"
        )
        response = client.chat.completions.create(
            model="deepseek-reasoner",
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=10
        )
        print("✅ DeepSeek API 连接成功")
        return True
    except Exception as e:
        if "402" in str(e) or "Insufficient Balance" in str(e):
            print("❌ DeepSeek API 余额不足")
        else:
            print(f"❌ DeepSeek API 连接失败: {e}")
        return False

def test_siliconflow():
    """测试 SiliconFlow API"""
    try:
        import openai
        client = openai.OpenAI(
            api_key=os.getenv("SILICONFLOW_API_KEY"),
            base_url="https://api.siliconflow.cn/v1"
        )
        response = client.chat.completions.create(
            model="deepseek-ai/DeepSeek-R1",
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=10
        )
        print("✅ SiliconFlow API 连接成功")
        return True
    except Exception as e:
        print(f"❌ SiliconFlow API 连接失败: {e}")
        return False

def test_ollama():
    """测试 Ollama"""
    try:
        import subprocess
        result = subprocess.run(
            ["ollama", "run", "qwen3", "Hello"],
            capture_output=True,
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            print("✅ Ollama 连接成功")
            return True
        else:
            print(f"❌ Ollama 连接失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Ollama 连接失败: {e}")
        return False

def main():
    print("🧪 测试 API 连接...\n")
    
    working_apis = []
    
    # 测试各种 API
    if os.getenv("OPENAI_API_KEY"):
        print("🔍 测试 OpenAI API...")
        if test_openai():
            working_apis.append("OpenAI")
    
    if os.getenv("DEEPSEEK_API_KEY"):
        print("🔍 测试 DeepSeek API...")
        if test_deepseek():
            working_apis.append("DeepSeek")
    
    if os.getenv("SILICONFLOW_API_KEY"):
        print("🔍 测试 SiliconFlow API...")
        if test_siliconflow():
            working_apis.append("SiliconFlow")
    
    # 测试 Ollama
    print("🔍 测试 Ollama...")
    if test_ollama():
        working_apis.append("Ollama")
    
    # 总结结果
    print(f"\n📊 测试结果:")
    if working_apis:
        print(f"✅ 可用的 API: {', '.join(working_apis)}")
        print("\n🎉 你可以运行以下程序:")
        if "OpenAI" in working_apis or "DeepSeek" in working_apis:
            print("  - python main.py")
        print("  - python free_llm_config.py")
    else:
        print("❌ 没有可用的 API")
        print("\n💡 建议:")
        print("1. 运行 python setup_free_api.py 获取免费 API")
        print("2. 或者安装本地 Ollama: brew install ollama")
        print("3. 或者充值 DeepSeek 账户")

if __name__ == "__main__":
    main()
