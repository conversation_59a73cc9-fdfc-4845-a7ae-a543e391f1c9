#!/usr/bin/env python3
"""
使用 OpenRouter API 的 DeepSearcher 配置
"""

def main():
    from deepsearcher.configuration import Configuration, init_config
    from deepsearcher.online_query import query
    import os

    config = Configuration()

    # 设置 OpenRouter API Key
    openrouter_key = "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"
    
    print("✅ 使用 OpenRouter API")
    print("🔍 模型: qwen/qwen-2.5-72b-instruct (免费)")
    
    # 设置环境变量（deepsearcher 可能需要这个）
    os.environ["OPENAI_API_KEY"] = openrouter_key
    os.environ["OPENAI_BASE_URL"] = "https://openrouter.ai/api/v1"

    # 配置 OpenRouter LLM
    config.set_provider_config("llm", "OpenAI", {
        "model": "qwen/qwen-2.5-72b-instruct",  # 免费的 Qwen 模型
        "base_url": "https://openrouter.ai/api/v1",
        "api_key": openrouter_key
    })

    # Embedding 配置
    config.set_provider_config(
        "embedding",
        "MilvusEmbedding",
        {
            "model": "BAAI/bge-base-en-v1.5",
        },
    )

    # 文件加载器配置
    config.set_provider_config(
        "file_loader",
        "PDFLoader",
        {},
    )

    # 向量数据库配置
    config.set_provider_config(
        "vector_db",
        "Milvus",
        {
            "default_collection": "deepsearcher",
            "uri": "./milvus.db",
            "token": "root:Milvus",
            "db": "default"
        },
    )

    # 初始化配置
    print("🔧 初始化配置...")
    init_config(config=config)

    # 加载本地数据
    print("📄 加载 PDF 文件...")
    from deepsearcher.offline_loading import load_from_local_files
    load_from_local_files(paths_or_directory="./tmpki79mvp__content_list.json")

    # 查询
    print("🤖 开始查询...")
    try:
        result = query(
            "You need to extract specific information from the PDF including basic information (project name, bidding number, tenderer information, agency), commercial requirements (bidding time, location, bidder qualifications, bid security, performance security, payment terms, price composition, registered capital, qualification grade, performance requirements), technical requirements (technical specifications, performance indicators, quality standards, acceptance criteria, personnel requirements, delivery requirements, maintenance requirements, certification materials), bid rejection items (bid rejection conditions), and scoring items (scoring standards)."
        )
        print("\n" + "="*50)
        print("📋 查询结果:")
        print("="*50)
        print(result)
        print("="*50)
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        print("\n💡 可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 确认 OpenRouter API key 有效")
        print("3. 尝试其他免费模型")

if __name__ == "__main__":
    main()
