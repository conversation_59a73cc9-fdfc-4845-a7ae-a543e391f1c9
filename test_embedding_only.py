#!/usr/bin/env python3
"""
仅测试 embedding 部分，不需要 LLM API key
"""

from deepsearcher.configuration import Configuration, init_config

def test_embedding_initialization():
    """测试 embedding 初始化"""
    print("🔧 测试 embedding 初始化...")
    
    config = Configuration()
    
    # 配置 embedding
    config.set_provider_config(
        "embedding",
        "MilvusEmbedding",
        {
            "model": "BAAI/bge-base-en-v1.5",
        },
    )
    
    # 配置向量数据库
    config.set_provider_config(
        "vector_db",
        "Milvus",
        {
            "default_collection": "deepsearcher",
            "uri": "./milvus.db",
            "token": "root:Milvus",
            "db": "default"
        },
    )
    
    # 配置文件加载器
    config.set_provider_config(
        "file_loader",
        "PDFLoader",
        {},
    )
    
    try:
        # 初始化配置（不包含 LLM）
        init_config(config=config)
        print("✅ Embedding 和向量数据库初始化成功！")
        print("✅ 第一个错误（SentenceTransformer uri 参数）已解决")
        return True
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 测试 embedding 配置（不需要 LLM API key）...\n")
    
    success = test_embedding_initialization()
    
    if success:
        print("\n🎉 Embedding 配置测试成功！")
        print("\n📝 下一步:")
        print("1. 设置 OPENAI_API_KEY 或 DEEPSEEK_API_KEY 环境变量")
        print("2. 运行 main.py 进行完整测试")
        print("\n💡 设置环境变量的方法:")
        print("   export OPENAI_API_KEY=your_api_key_here")
        print("   或")
        print("   export DEEPSEEK_API_KEY=your_api_key_here")
    else:
        print("\n❌ 还有配置问题需要解决")

if __name__ == "__main__":
    main()
