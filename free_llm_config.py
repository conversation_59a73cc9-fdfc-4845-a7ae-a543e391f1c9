#!/usr/bin/env python3
"""
免费 LLM 配置选项
解决 DeepSeek API 余额不足问题
"""

def main():
    from deepsearcher.configuration import Configuration, init_config
    from deepsearcher.online_query import query
    import os

    config = Configuration()

    # 免费 LLM 选项配置
    print("🔍 检查可用的免费 LLM 选项...")
    
    # 选项 1: OpenAI（如果有 API key）
    if os.getenv("OPENAI_API_KEY"):
        print("✅ 使用 OpenAI")
        config.set_provider_config("llm", "OpenAI", {"model": "gpt-3.5-turbo"})
    
    # 选项 2: 使用 SiliconFlow（免费额度）
    elif os.getenv("SILICONFLOW_API_KEY"):
        print("✅ 使用 SiliconFlow（免费）")
        config.set_provider_config("llm", "SiliconFlow", {
            "model": "deepseek-ai/DeepSeek-R1"
        })
    
    # 选项 3: 使用 OpenRouter（免费模型）
    elif os.getenv("OPENROUTER_API_KEY"):
        print("✅ 使用 OpenRouter（免费模型）")
        config.set_provider_config("llm", "OpenAI", {
            "model": "qwen/qwen3-235b-a22b:free",
            "base_url": "https://openrouter.ai/api/v1",
            "api_key": os.getenv("OPENROUTER_API_KEY")
        })
    
    # 选项 4: 使用本地 Ollama（完全免费）
    elif os.path.exists("/usr/local/bin/ollama") or os.path.exists("/opt/homebrew/bin/ollama"):
        print("✅ 使用本地 Ollama")
        config.set_provider_config("llm", "Ollama", {
            "model": "qwen3"
        })
    
    # 选项 5: DeepSeek（如果有余额）
    elif os.getenv("DEEPSEEK_API_KEY"):
        print("⚠️  尝试使用 DeepSeek（可能余额不足）")
        config.set_provider_config("llm", "DeepSeek", {"model": "deepseek-reasoner"})
    
    else:
        print("❌ 没有找到可用的 LLM API key")
        print("\n🆓 免费选项:")
        print("1. SiliconFlow: https://siliconflow.cn/ (注册获取免费额度)")
        print("   export SILICONFLOW_API_KEY=your_key")
        print("\n2. OpenRouter: https://openrouter.ai/ (有免费模型)")
        print("   export OPENROUTER_API_KEY=your_key")
        print("\n3. 本地 Ollama: brew install ollama && ollama pull qwen3")
        print("\n4. OpenAI: export OPENAI_API_KEY=your_key")
        print("\n5. 充值 DeepSeek: https://platform.deepseek.com/")
        exit(1)

    # Embedding 配置（保持不变）
    config.set_provider_config(
        "embedding",
        "MilvusEmbedding",
        {
            "model": "BAAI/bge-base-en-v1.5",
        },
    )

    # 文件加载器配置
    config.set_provider_config(
        "file_loader",
        "PDFLoader",
        {},
    )

    # 向量数据库配置
    config.set_provider_config(
        "vector_db",
        "Milvus",
        {
            "default_collection": "deepsearcher",
            "uri": "./milvus.db",
            "token": "root:Milvus",
            "db": "default"
        },
    )

    # 初始化配置
    init_config(config=config)

    # 加载本地数据
    from deepsearcher.offline_loading import load_from_local_files
    load_from_local_files(paths_or_directory="./tmpki79mvp__origin.pdf")

    # 查询
    try:
        result = query(
            "You need to extract specific information from the PDF including basic information (project name, bidding number, tenderer information, agency), commercial requirements (bidding time, location, bidder qualifications, bid security, performance security, payment terms, price composition, registered capital, qualification grade, performance requirements), technical requirements (technical specifications, performance indicators, quality standards, acceptance criteria, personnel requirements, delivery requirements, maintenance requirements, certification materials), bid rejection items (bid rejection conditions), and scoring items (scoring standards)."
        )
        print(result)
    except Exception as e:
        if "402" in str(e) or "Insufficient Balance" in str(e):
            print("❌ API 余额不足！")
            print("💡 请尝试以下解决方案:")
            print("1. 使用其他免费 API（SiliconFlow、OpenRouter）")
            print("2. 安装本地 Ollama")
            print("3. 充值当前 API 账户")
        else:
            print(f"❌ 其他错误: {e}")

if __name__ == "__main__":
    main()
