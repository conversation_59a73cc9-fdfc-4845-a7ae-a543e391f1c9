#!/usr/bin/env python3
"""
测试 OpenRouter API 连接
"""

import openai

def test_openrouter():
    """测试 OpenRouter API"""
    openrouter_key = "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"
    
    print("🧪 测试 OpenRouter API 连接...")
    
    try:
        client = openai.OpenAI(
            api_key=openrouter_key,
            base_url="https://openrouter.ai/api/v1"
        )
        
        print("📡 发送测试请求...")
        response = client.chat.completions.create(
            model="qwen/qwen-2.5-72b-instruct",  # 免费模型
            messages=[{"role": "user", "content": "Hello, please respond with 'API connection successful'"}],
            max_tokens=20
        )
        
        result = response.choices[0].message.content
        print(f"✅ OpenRouter API 连接成功!")
        print(f"📝 响应: {result}")
        return True
        
    except Exception as e:
        print(f"❌ OpenRouter API 连接失败: {e}")
        
        # 尝试其他免费模型
        print("\n🔄 尝试其他免费模型...")
        try:
            response = client.chat.completions.create(
                model="microsoft/phi-3-mini-128k-instruct:free",  # 另一个免费模型
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            print("✅ 使用 Phi-3 模型连接成功!")
            return True
        except Exception as e2:
            print(f"❌ 备用模型也失败: {e2}")
            return False

def main():
    print("🚀 测试 OpenRouter API...\n")
    
    if test_openrouter():
        print("\n🎉 API 测试成功！可以运行主程序了")
        print("运行命令: python main_openrouter.py")
    else:
        print("\n❌ API 测试失败")
        print("💡 可能的原因:")
        print("1. API key 无效或过期")
        print("2. 网络连接问题")
        print("3. OpenRouter 服务暂时不可用")

if __name__ == "__main__":
    main()
